[DEBUG] TestResultView.get called, token=1, result_id=None
[DEBUG] TestResultView.get called, token=1, result_id=None
[DEBUG] TestResultView.get called, token=1, result_id=None
[DEBUG] TestResultView.get called, token=1, result_id=None
[DEBUG] TestResultView.get called, token=1, result_id=None
[DEBUG] TestResultView.get called, token=1, result_id=None
[DEBUG] TestResultView.get called, token=1, result_id=None
[DEBUG] TestResultView.get called, token=1, result_id=None
[DEBUG] TestResultView.get called, token=1, result_id=None
[DEBUG] TestResultView.get called, token=1, result_id=None
[DEBUG] Returning 1 results: [{'id': 1, 'execution_id': 2, 'plan_name': '测试计划', 'task_name': '测试任务', 'source_type': 'execution', 'log_source_host': None, 'log_source_path': None, 'log_extraction_method': None, 'metrics': [{'label': 'AverageUserSpeed', 'value': '30.283630', 'unit': 'GB/s', 'confidence': 0.98}, {'label': 'AveragePCIeSpeed', 'value': '30.429640', 'unit': 'GB/s', 'confidence': 0.98}, {'label': 'AverageUserSpeed', 'value': '54.126707', 'unit': 'GB/s', 'confidence': 0.98}, {'label': 'AveragePCIeSpeed', 'value': '54.581893', 'unit': 'GB/s', 'confidence': 0.98}], 'raw_log': '', 'total_metrics': 4, 'confirmed_metrics': 4, 'ai_confidence': 0.0, 'success_rate': 100.0, 'is_standalone': False, 'created_at': '2025-07-08 14:25:15', 'updated_at': '2025-07-16 17:22:53', 'created_by_id': 1}]
[DEBUG] json_response content: {"data": [{"id": 1, "execution_id": 2, "plan_name": "测试计划", "task_name": "测试任务", "source_type": "execution", "log_source_host": null, "log_source_path": null, "log_extraction_method": null, "metrics": [{"label": "AverageUserSpeed", "value": "30.283630", "unit": "GB/s", "confidence": 0.98}, {"label": "AveragePCIeSpeed", "value": "30.429640", "unit": "GB/s", "confidence": 0.98}, {"label": "AverageUserSpeed", "value": "54.126707", "unit": "GB/s", "confidence": 0.98}, {"label": "AveragePCIeSpeed", "value": "54.581893", "unit": "GB/s", "confidence": 0.98}], "raw_log": "", "total_metrics": 4, "confirmed_metrics": 4, "ai_confidence": 0.0, "success_rate": 100.0, "is_standalone": false, "created_at": "2025-07-08 14:25:15", "updated_at": "2025-07-16 17:22:53", "created_by_id": 1}], "error": ""}
[DEBUG] TestResultView.get called, token=1, result_id=1

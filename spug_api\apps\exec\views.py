# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.
from django.views.generic import View
from django.db import close_old_connections, transaction
from django.conf import settings
from libs import JsonParser, Argument, json_response, auth, human_datetime
from apps.account.utils import has_host_perm
from apps.host.models import Host
from apps.setting.utils import AppSetting
from apps.app.models import Deploy
from apps.schedule.models import Task
from apps.notify.models import Notify
from apps.account.models import User
from apps.exec.models import ExecTemplate, TestPlan, TestCaseSet
from libs.ssh import SSH
from concurrent import futures
from datetime import datetime
from threading import Thread
from functools import partial
import json
import socket
import subprocess
import json
import uuid
import os


class TaskView(View):
    @auth('exec.task.do')
    def get(self, request):
        host_id = request.GET.get('host_id')
        if not host_id:
            return json_response(error='未指定主机')
        history = []
        for item in request.user.exec_task.all():
            data = item.to_dict()
            if not data['is_public'] and data['created_by_id'] != request.user.id:
                continue
            data['alias'] = item.host.name if item.host_id else None
            if host_id and item.host_id != int(host_id):
                continue
            data['command'] = item.command
            history.append(data)
        return json_response(history)

    @auth('exec.task.do')
    def post(self, request):
        form, error = JsonParser(
            Argument('host_id', type=int, help='请选择主机'),
            Argument('command', help='请输入要执行的命令')
        ).parse(request.body)
        if error:
            return json_response(error=error)

        host = Host.objects.filter(pk=form.host_id).first()
        if not host:
            return json_response(error='未找到指定主机')
        if not has_host_perm(request.user, host.id):
            return json_response(error='您无权访问该主机')

        task = request.user.exec_task.create(
            host_id=form.host_id,
            command=form.command,
            is_public=False
        )
        return json_response(task.id)

    @auth('exec.task.edit|exec.task.del')
    def patch(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='请指定操作对象'),
            Argument('is_public', type=bool, required=False)
        ).parse(request.body)
        if error:
            return json_response(error=error)

        task = request.user.exec_task.filter(pk=form.id).first()
        if not task:
            return json_response(error='未找到指定记录')

        task.is_public = form.is_public
        task.save()
        return json_response()

    @auth('exec.task.del')
    def delete(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='请指定操作对象')
        ).parse(request.GET)
        if error:
            return json_response(error=error)

        task = request.user.exec_task.filter(pk=form.id).first()
        if task:
            task.delete()
        return json_response()


class BatchView(View):
    @auth('exec.task.batch')
    def post(self, request):
        form, error = JsonParser(
            Argument('host_ids', type=list, filter=lambda x: len(x), help='请选择执行主机'),
            Argument('command', help='请输入要执行的命令'),
            Argument('interpreter', default='sh')
        ).parse(request.body)
        if error:
            return json_response(error=error)

        if not has_host_perm(request.user, form.host_ids):
            return json_response(error='您无权访问该主机')

        token = uuid.uuid4().hex
        outputs = {}
        for host_id in form.host_ids:
            outputs[host_id] = {'id': host_id, 'output': f'### Executing: {form.command}\n'}

        def do_exec(host_id):
            host = Host.objects.get(pk=host_id)
            cli = host.get_ssh()
            code = -1
            try:
                code, out = cli.exec_command(form.command, form.interpreter)
                outputs[host.id]['output'] += out
            except Exception as e:
                outputs[host.id]['output'] += f'Exception: {e}\n'
            finally:
                outputs[host.id]['status'] = 'success' if code == 0 else 'error'
                outputs[host.id]['output'] += f'\n### Exit code: {code}\n'
                cli.close()

        threads = []
        for host_id in form.host_ids:
            t = Thread(target=do_exec, args=(host_id,))
            threads.append(t)
            t.start()
        for t in threads:
            t.join()

        return json_response({'token': token, 'outputs': outputs})


class BatchCommand(View):
    @auth('exec.task.batch')
    def post(self, request):
        form, error = JsonParser(
            Argument('host_ids', type=list, filter=lambda x: len(x), help='请选择执行主机'),
            Argument('command', help='请输入要执行的命令'),
            Argument('interpreter', default='sh')
        ).parse(request.body)
        if error:
            return json_response(error=error)

        if not has_host_perm(request.user, form.host_ids):
            return json_response(error='您无权访问该主机')

        token = uuid.uuid4().hex
        outputs = {}
        for host_id in form.host_ids:
            outputs[host_id] = {'id': host_id, 'output': f'### Executing: {form.command}\n'}

        def do_exec(host_id):
            host = Host.objects.get(pk=host_id)
            cli = host.get_ssh()
            code = -1
            try:
                code, out = cli.exec_command(form.command, form.interpreter)
                outputs[host.id]['output'] += out
            except Exception as e:
                outputs[host.id]['output'] += f'Exception: {e}\n'
            finally:
                outputs[host.id]['status'] = 'success' if code == 0 else 'error'
                outputs[host.id]['output'] += f'\n### Exit code: {code}\n'
                cli.close()

        threads = []
        for host_id in form.host_ids:
            t = Thread(target=do_exec, args=(host_id,))
            threads.append(t)
            t.start()
        for t in threads:
            t.join()

        return json_response({'token': token, 'outputs': outputs})


class TemplateView(View):
    @auth('exec.template.view')
    def get(self, request):
        templates = ExecTemplate.objects.all()
        types = [x['type'] for x in templates.order_by('type').values('type').distinct()]
        return json_response({'types': types, 'templates': [x.to_dict() for x in templates]})

    @auth('exec.template.add')
    def post(self, request):
        form, error = JsonParser(
            Argument('name', help='请输入模版名称'),
            Argument('type', help='请选择模版类型'),
            Argument('body', help='请输入模版内容'),
            Argument('interpreter', default='sh'),
            Argument('host_ids', type=list, default=[]),
            Argument('desc', required=False)
        ).parse(request.body)
        if error:
            return json_response(error=error)

        if ExecTemplate.objects.filter(name=form.name, type=form.type).exists():
            return json_response(error=f'已存在的模板名称【{form.name}】')

        ExecTemplate.objects.create(
            name=form.name,
            type=form.type,
            body=form.body,
            interpreter=form.interpreter,
            host_ids=json.dumps(form.host_ids),
            desc=form.desc
        )
        return json_response()

    @auth('exec.template.edit')
    def patch(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='请指定操作对象'),
            Argument('name', help='请输入模版名称'),
            Argument('type', help='请选择模版类型'),
            Argument('body', help='请输入模版内容'),
            Argument('interpreter', default='sh'),
            Argument('host_ids', type=list, default=[]),
            Argument('desc', required=False)
        ).parse(request.body)
        if error:
            return json_response(error=error)

        if ExecTemplate.objects.filter(name=form.name, type=form.type).exclude(id=form.id).exists():
            return json_response(error=f'已存在的模板名称【{form.name}】')

        ExecTemplate.objects.filter(pk=form.id).update(
            name=form.name,
            type=form.type,
            body=form.body,
            interpreter=form.interpreter,
            host_ids=json.dumps(form.host_ids),
            desc=form.desc
        )
        return json_response()

    @auth('exec.template.del')
    def delete(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='请指定操作对象')
        ).parse(request.GET)
        if error:
            return json_response(error=error)

        ExecTemplate.objects.filter(pk=form.id).delete()
        return json_response()


class FileView(View):
    @auth('exec.task.do')
    def post(self, request):
        form, error = JsonParser(
            Argument('host_id', type=int, help='请选择主机'),
            Argument('path', help='请输入文件路径'),
            Argument('token', help='请输入验证令牌')
        ).parse(request.body)
        if error:
            return json_response(error=error)

        if not has_host_perm(request.user, form.host_id):
            return json_response(error='您无权访问该主机')

        host = Host.objects.filter(pk=form.host_id).first()
        if not host:
            return json_response(error='未找到指定主机')

        cli = host.get_ssh()
        try:
            with cli.get_client() as c:
                sftp = c.open_sftp()
                f_stat = sftp.stat(form.path)

                if not request.user.is_supper:
                    if stat.S_ISDIR(f_stat.st_mode):
                        return json_response(error='该路径是一个目录，需要超级管理员权限查看目录')

                    if not request.user.has_host_perm(host.id):
                        return json_response(error='您无权访问该主机')

                if stat.S_ISDIR(f_stat.st_mode):
                    return json_response(error='该路径是一个目录，请输入文件路径')

                if f_stat.st_size > 1024 * 1024 * 10:
                    return json_response(error='文件大小超过10M，请使用下载功能')

                f = sftp.open(form.path)
                content = f.read().decode(errors='ignore')
                f.close()
                return json_response(content)
        except Exception as e:
            return json_response(error=f'读取文件失败：{e}')
        finally:
            cli.close()


class ExecView(View):
    @auth('exec.task.do')
    def post(self, request):
        form, error = JsonParser(
            Argument('host_id', type=int, help='请选择主机'),
            Argument('command', help='请输入要执行的命令'),
            Argument('interpreter', default='sh')
        ).parse(request.body)
        if error:
            return json_response(error=error)

        host = Host.objects.filter(pk=form.host_id).first()
        if not host:
            return json_response(error='未找到指定主机')
        if not has_host_perm(request.user, host.id):
            return json_response(error='您无权访问该主机')

        cli = host.get_ssh()
        command = form.command
        try:
            code, out = cli.exec_command(command, form.interpreter)
            status = 'success' if code == 0 else 'error'
            return json_response({
                'status': status,
                'output': out,
                'command': command
            })
        except Exception as e:
            return json_response(error=f'执行失败：{e}')
        finally:
            cli.close()


class TransferView(View):
    @auth('exec.transfer.do')
    def post(self, request):
        form, error = JsonParser(
            Argument('host_id', type=int, help='请选择主机'),
            Argument('src_path', help='请输入源路径'),
            Argument('dst_path', help='请输入目标路径'),
            Argument('host_type', help='请选择操作类型'),
        ).parse(request.body)
        if error:
            return json_response(error=error)

        host = Host.objects.filter(pk=form.host_id).first()
        if not host:
            return json_response(error='未找到指定主机')
        if not has_host_perm(request.user, host.id):
            return json_response(error='您无权访问该主机')

        cli = host.get_ssh()
        try:
            if form.host_type == 'parse':
                return json_response(cli.list_dir_attr(form.src_path))
            elif form.host_type == 'get':
                return json_response(cli.download_file(form.src_path, form.dst_path))
            elif form.host_type == 'put':
                return json_response(cli.upload_file(form.src_path, form.dst_path))
            else:
                return json_response(error='未识别的操作类型')
        except Exception as e:
            return json_response(error=f'传输失败：{e}')
        finally:
            cli.close()


class WebTerminalView(View):
    @auth('exec.console.view|exec.console.list')
    def get(self, request):
        form, error = JsonParser(
            Argument('id', type=int, required=False),
            Argument('type', required=False),
            Argument('aid', type=int, required=False),
            Argument('app_id', type=int, required=False),
            Argument('env_id', type=int, required=False),
        ).parse(request.GET)

        if not request.user.has_perms(['exec.console.view', 'exec.console.list']):
            return json_response(error='权限拒绝')

        if not form.id:
            return json_response(error='请指定主机id')

        host = Host.objects.filter(pk=form.id).first()
        if not host:
            return json_response(error='未找到指定主机')
        if not has_host_perm(request.user, host.id):
            return json_response(error='您无权访问该主机')

        try:
            cli = host.get_ssh()
            cli.ping()
        except Exception as e:
            return json_response(error=f'ssh连接失败：{e}')

        if form.type == 'exec':
            if not request.user.has_perms(['exec.console.list']):
                return json_response(error='权限拒绝')

            if form.aid:
                deploy = Deploy.objects.filter(pk=form.aid).first()
                if not deploy:
                    return json_response(error='未找到指定应用')
                if not deploy.extend_obj.env.id == form.env_id:
                    return json_response(error='错误的环境ID')
                command = deploy.extend_obj.hook_plan
            else:
                command = AppSetting.get_default('exec_command')
            return json_response({
                'token': uuid.uuid4().hex,
                'type': 'exec',
                'command': command
            })
        else:
            if not request.user.has_perms(['exec.console.view']):
                return json_response(error='权限拒绝')

            if host.username == 'root' and not host.pkey:
                return json_response(error='禁止以root用户直接登录，请参考官方文档部署密钥登录')

            token = uuid.uuid4().hex
            return json_response({
                'token': token,
                'type': 'web',
                'size': request.GET.get('size', '80x24'),
                'host': host.hostname,
                'port': host.port,
                'username': host.username,
                'password': host.password,
                'pkey': host.pkey,
            })

    @auth('exec.console.view')
    def post(self, request):
        form, error = JsonParser(
            Argument('token', help='参数错误'),
            Argument('hostname', help='参数错误'),
            Argument('port', type=int, help='参数错误'),
            Argument('username', help='参数错误'),
            Argument('password', required=False),
            Argument('pkey', required=False),
            Argument('size', default='80x24'),
        ).parse(request.body)

        if error:
            return json_response(error=error)

        try:
            cli = SSH(form.hostname, form.port, form.username, form.password, form.pkey)
            cli.ping()
        except Exception as e:
            return json_response(error=f'ssh连接失败：{e}')

        width, height = form.size.split('x')
        width, height = int(width), int(height)
        options = {
            'custom_log': False,
            'token': form.token,
            'width': width,
            'height': height
        }
        try:
            websocket = WebTerminalConsumer(options)
            websocket.init_session(cli)
            return json_response()
        except Exception as e:
            return json_response(error=f'创建会话失败：{e}')


class WebTerminalExecuteView(View):
    @auth('exec.console.list')
    def post(self, request):
        form, error = JsonParser(
            Argument('token', help='参数错误'),
            Argument('hostname', help='参数错误'),
            Argument('port', type=int, help='参数错误'),
            Argument('username', help='参数错误'),
            Argument('command', help='参数错误'),
            Argument('password', required=False),
            Argument('pkey', required=False),
        ).parse(request.body)

        if error:
            return json_response(error=error)

        try:
            cli = SSH(form.hostname, form.port, form.username, form.password, form.pkey)
            cli.ping()
        except Exception as e:
            return json_response(error=f'ssh连接失败：{e}')

        options = {
            'custom_log': False,
            'token': form.token,
            'width': 80,
            'height': 24
        }
        try:
            websocket = WebTerminalExecuteConsumer(options)
            websocket.init_session(cli, form.command)
            return json_response()
        except Exception as e:
            return json_response(error=f'创建会话失败：{e}')


class WebTerminalCloseView(View):
    @auth('exec.console.view')
    def post(self, request):
        form, error = JsonParser(
            Argument('token', help='参数错误'),
        ).parse(request.body)

        if error:
            return json_response(error=error)

        try:
            ws = WebTerminalConsumer.find_by_token(form.token)
            ws.disconnect(True)
        except:
            pass
        finally:
            return json_response()


class TestResultView(View):
    """测试结果收集API"""
    
    def get(self, request, result_id=None):
        """获取测试结果列表或单个测试结果"""
        # 检查开发者后门token
        token = request.GET.get('token')
        if token == '1':
            # 开发者模式，返回模拟数据
            mock_data = [
                {
                    "id": 1,
                    "plan_name": "性能测试计划A",
                    "task_name": "CPU压力测试",
                    "source_type": "execution",
                    "total_metrics": 3,
                    "confirmed_metrics": 2,
                    "ai_confidence": 0.85,
                    "created_at": "2024-01-15 10:30:00",
                    "updated_at": "2024-01-15 10:35:00",
                    "metrics": [
                        {"name": "CPU使用率", "value": "75%", "unit": "%", "threshold": "80%", "status": "正常"},
                        {"name": "内存使用率", "value": "60%", "unit": "%", "threshold": "70%", "status": "正常"},
                        {"name": "响应时间", "value": "120ms", "unit": "ms", "threshold": "200ms", "status": "正常"}
                    ]
                },
                {
                    "id": 2,
                    "plan_name": "性能测试计划B",
                    "task_name": "内存压力测试",
                    "source_type": "log_extraction",
                    "total_metrics": 4,
                    "confirmed_metrics": 3,
                    "ai_confidence": 0.92,
                    "created_at": "2024-01-15 11:00:00",
                    "updated_at": "2024-01-15 11:05:00",
                    "metrics": [
                        {"name": "内存使用率", "value": "85%", "unit": "%", "threshold": "90%", "status": "正常"},
                        {"name": "磁盘IO", "value": "45MB/s", "unit": "MB/s", "threshold": "50MB/s", "status": "正常"},
                        {"name": "网络延迟", "value": "15ms", "unit": "ms", "threshold": "20ms", "status": "正常"},
                        {"name": "吞吐量", "value": "1200req/s", "unit": "req/s", "threshold": "1000req/s", "status": "优秀"}
                    ]
                },
                {
                    "id": 3,
                    "plan_name": "稳定性测试计划",
                    "task_name": "长时间运行测试",
                    "source_type": "execution",
                    "total_metrics": 2,
                    "confirmed_metrics": 1,
                    "ai_confidence": 0.78,
                    "created_at": "2024-01-15 12:00:00",
                    "updated_at": "2024-01-15 12:30:00",
                    "metrics": [
                        {"name": "系统稳定性", "value": "99.5%", "unit": "%", "threshold": "99%", "status": "优秀"},
                        {"name": "错误率", "value": "0.1%", "unit": "%", "threshold": "1%", "status": "正常"}
                    ]
                }
            ]
            
            if result_id:
                # 返回单个测试结果
                for item in mock_data:
                    if item["id"] == int(result_id):
                        return json_response(item)
                return json_response(error='测试结果不存在')
            else:
                # 返回测试结果列表
                return json_response(mock_data)
        
        # 正常权限验证流程
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return json_response(error='验证失败，请重新登录', status=401)
        
        # 检查权限
        if not request.user.has_perms(['exec.task.do']):
            return json_response(error='权限不足', status=403)
        
        try:
            from .models import TestResult
            
            if result_id:
                # 获取单个测试结果
                try:
                    result = TestResult.objects.get(pk=result_id)
                    
                    # 获取并处理指标数据
                    metrics = result.get_metrics()
                    
                    return json_response(result.to_dict())
                except TestResult.DoesNotExist:
                    return json_response(error='测试结果不存在')
            else:
                # 获取测试结果列表
                plan_name = request.GET.get('plan_name')
                task_name = request.GET.get('task_name')
                source_type = request.GET.get('source_type')
                date_from = request.GET.get('date_from')
                date_to = request.GET.get('date_to')
                
                query = TestResult.objects.all()
                
                if plan_name:
                    query = query.filter(plan_name__icontains=plan_name)
                if task_name:
                    query = query.filter(task_name__icontains=task_name)
                if source_type:
                    query = query.filter(source_type=source_type)
                if date_from and date_to:
                    from datetime import datetime
                    try:
                        date_from = datetime.strptime(date_from, '%Y-%m-%d')
                        date_to = datetime.strptime(date_to + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                        query = query.filter(created_at__range=(date_from, date_to))
                    except ValueError:
                        pass
                
                results = [result.to_dict() for result in query]
                
                return json_response(results)
                
        except Exception as e:
            return json_response(error=f'获取测试结果失败: {str(e)}')
    
    @auth('exec.task.do')
    def put(self, request, result_id):
        """更新测试结果"""
        try:
            from .models import TestResult
            from django.utils import timezone
            
            # 获取测试结果
            try:
                result = TestResult.objects.get(pk=result_id)
            except TestResult.DoesNotExist:
                return json_response(error=f'测试结果ID {result_id} 不存在')
            
            # 解析请求数据
            data = json.loads(request.body)
            plan_name = data.get('plan_name')
            task_name = data.get('task_name')
            confirmed_metrics = data.get('confirmed_metrics')
            
            # 更新测试结果
            if plan_name is not None:
                result.plan_name = plan_name
            if task_name is not None:
                result.task_name = task_name
            if confirmed_metrics is not None:
                result.confirmed_metrics = confirmed_metrics
            
            # 更新时间
            result.updated_at = timezone.now()
            result.save()
            
            return json_response({'message': '测试结果已更新'})
        except Exception as e:
            return json_response(error=f'更新测试结果失败: {str(e)}')
    
    @auth('exec.task.do')
    def post(self, request):
        """保存测试结果"""
        try:
            from .models import TestResult, TestPlanExecution
            from django.db import transaction
            from django.utils import timezone
            from libs import human_datetime
            
            data = json.loads(request.body)
            execution_id = data.get('execution_id')
            plan_name = data.get('plan_name')
            task_name = data.get('task_name', '')
            metrics = data.get('metrics', [])
            raw_log = data.get('raw_log', '')
            log_path = data.get('log_path', '')
            confirmed_metrics = data.get('confirmed_metrics', 0)
            ai_confidence = data.get('ai_confidence', 0.0)
            source = data.get('source', '')
            
            # 获取额外参数
            log_source_host = data.get('log_source_host', '')
            log_source_path = data.get('log_source_path', '')
            log_extraction_method = data.get('log_extraction_method', '')
            source_type = data.get('source_type', 'execution')
            
            # 检查是否为独立日志提取模式
            is_standalone = source == 'remote_extraction' or source_type == 'log_extraction' or not execution_id
            
            # 验证必要参数
            if not plan_name:
                return json_response(error='缺少测试计划名称')
            
            if not metrics:
                return json_response(error='请至少提供一个性能指标')
            
            # 准备metrics数据
            metrics_json = json.dumps(metrics, ensure_ascii=False)
            
            # 使用Django ORM和事务来避免数据库锁定问题
            with transaction.atomic():
                try:
                    execution = None
                    user_id = request.user.id if hasattr(request, 'user') and request.user.id else 1
                    
                    if not is_standalone and execution_id:
                        # 测试计划执行模式 - 验证执行记录ID
                        try:
                            execution = TestPlanExecution.objects.get(pk=execution_id)
                            user_id = getattr(execution, 'executor_id', 1)
                            source_type = 'execution'
                        except TestPlanExecution.DoesNotExist:
                            # 列出所有可用的TestPlanExecution记录
                            all_executions = TestPlanExecution.objects.all()
                            available_ids = [str(h.id) for h in all_executions]
                            return json_response(error=f'执行记录ID {execution_id} 不存在。可用的ID: {", ".join(available_ids)}')
                    else:
                        # 独立日志提取模式
                        execution_id = None
                        if not source_type or source_type == 'execution':
                            source_type = 'log_extraction'
                    
                    # 检查是否已有相同的测试结果
                    if execution_id:
                        existing_result = TestResult.objects.filter(execution_id=execution_id).first()
                    else:
                        # 对于独立模式，检查是否有相同计划名称和任务名称的结果
                        existing_result = TestResult.objects.filter(
                            execution__isnull=True,
                            plan_name=plan_name,
                            task_name=task_name,
                            source_type=source_type
                        ).first()
                    
                    if existing_result:
                        # 更新现有结果
                        existing_result.plan_name = plan_name
                        existing_result.task_name = task_name
                        existing_result.source_type = source_type
                        existing_result.log_source_host = log_source_host
                        existing_result.log_source_path = log_source_path
                        existing_result.log_extraction_method = log_extraction_method
                        existing_result.metrics = metrics_json
                        existing_result.raw_log = raw_log or existing_result.raw_log
                        existing_result.total_metrics = len(metrics)
                        existing_result.confirmed_metrics = confirmed_metrics
                        existing_result.ai_confidence = ai_confidence
                        existing_result.updated_at = timezone.now()
                        existing_result.save()
                        
                        return json_response({
                            'message': '测试结果已更新',
                            'result_id': existing_result.id
                        })
                    else:
                        # 创建新的测试结果
                        new_result = TestResult.objects.create(
                            execution=execution,  # 可能为None
                            plan_name=plan_name,
                            task_name=task_name,
                            source_type=source_type,
                            log_source_host=log_source_host,
                            log_source_path=log_source_path,
                            log_extraction_method=log_extraction_method,
                            metrics=metrics_json,
                            raw_log=raw_log or log_path,  # 保存原始日志内容或日志路径
                            total_metrics=len(metrics),
                            confirmed_metrics=confirmed_metrics,
                            ai_confidence=ai_confidence,
                            created_by_id=user_id
                        )
                        
                        return json_response({
                            'message': '测试结果已保存',
                            'result_id': new_result.id
                        })
                        
                except Exception as e:
                    print(f"[ERROR] 保存测试结果失败: {str(e)}")
                    return json_response(error=f'保存测试结果失败: {str(e)}')
                
        except Exception as e:
            return json_response(error=f'保存测试结果失败: {str(e)}')
    
    @auth('exec.task.do')
    def delete(self, request, result_id):
        """删除测试结果"""
        try:
            from .models import TestResult
            
            result = TestResult.objects.filter(pk=result_id).first()
            if not result:
                return json_response(error='测试结果不存在')
            
            result.delete()
            return json_response({'message': '测试结果已删除'})
        except Exception as e:
            return json_response(error=f'删除测试结果失败: {str(e)}')


class TestPlanView(View):
    """测试计划API"""
    
    @auth('exec.task.do')
    def get(self, request, plan_id=None):
        """获取测试计划列表或单个测试计划"""
        try:
            if plan_id:
                # 获取单个测试计划
                try:
                    plan = TestPlan.objects.get(pk=plan_id)
                    return json_response(plan.to_dict())
                except TestPlan.DoesNotExist:
                    return json_response(error=f'测试计划ID {plan_id} 不存在')
            
            # 获取测试计划列表
            plans = TestPlan.objects.all().order_by('-id')
            return json_response([p.to_dict() for p in plans])
        except Exception as e:
            return json_response(error=f'获取测试计划失败: {str(e)}')
    
    @auth('exec.task.do')
    def post(self, request):
        """创建新测试计划"""
        try:
            data = json.loads(request.body)
            name = data.get('name')
            description = data.get('description')
            category = data.get('category')
            commands = data.get('commands', [])
            files = data.get('files', [])
            steps = data.get('steps', [])
            step_interval = data.get('step_interval', 0)
            file_path_strict = data.get('file_path_strict', False)
            variables = data.get('variables', [])
            
            if not name:
                return json_response(error='测试计划名称不能为空')
            
            # 创建新测试计划
            plan = TestPlan.objects.create(
                name=name,
                description=description,
                category=category,
                commands=json.dumps(commands),
                files=json.dumps(files),
                steps=json.dumps(steps),
                step_interval=step_interval,
                file_path_strict=file_path_strict,
                variables=json.dumps(variables),
                created_at=human_datetime(),
                created_by_id=request.user.id if hasattr(request, 'user') else 1,
                updated_at=human_datetime(),
                updated_by_id=request.user.id if hasattr(request, 'user') else 1
            )
            
            return json_response(plan.to_dict())
        except Exception as e:
            return json_response(error=f'创建测试计划失败: {str(e)}')
    
    @auth('exec.task.do')
    def put(self, request, plan_id):
        """更新测试计划"""
        try:
            try:
                plan = TestPlan.objects.get(pk=plan_id)
            except TestPlan.DoesNotExist:
                return json_response(error=f'测试计划ID {plan_id} 不存在')
            
            data = json.loads(request.body)
            name = data.get('name')
            description = data.get('description')
            category = data.get('category')
            commands = data.get('commands')
            files = data.get('files')
            steps = data.get('steps')
            step_interval = data.get('step_interval')
            file_path_strict = data.get('file_path_strict')
            variables = data.get('variables')
            
            if not name:
                return json_response(error='测试计划名称不能为空')
            
            # 更新测试计划
            if name is not None:
                plan.name = name
            if description is not None:
                plan.description = description
            if category is not None:
                plan.category = category
            if commands is not None:
                plan.commands = json.dumps(commands)
            if files is not None:
                plan.files = json.dumps(files)
            if steps is not None:
                plan.steps = json.dumps(steps)
            if step_interval is not None:
                plan.step_interval = step_interval
            if file_path_strict is not None:
                plan.file_path_strict = file_path_strict
            if variables is not None:
                plan.variables = json.dumps(variables)
            
            plan.updated_at = human_datetime()
            plan.updated_by_id = request.user.id if hasattr(request, 'user') else 1
            plan.save()
            
            return json_response(plan.to_dict())
        except Exception as e:
            return json_response(error=f'更新测试计划失败: {str(e)}')
    
    @auth('exec.task.do')
    def delete(self, request, plan_id=None):
        """删除测试计划"""
        try:
            if not plan_id:
                data = json.loads(request.body)
                plan_id = data.get('id')
                
            if not plan_id:
                return json_response(error='测试计划ID不能为空')
                
            try:
                plan = TestPlan.objects.get(pk=plan_id)
            except TestPlan.DoesNotExist:
                return json_response(error=f'测试计划ID {plan_id} 不存在')
            
            # 删除测试计划
            plan.delete()
            
            return json_response()
        except Exception as e:
            return json_response(error=f'删除测试计划失败: {str(e)}')


class TestCaseSetView(View):
    """测试用例集API"""
    
    @auth('exec.task.do')
    def get(self, request, case_set_id=None):
        """获取测试用例集列表或单个测试用例集"""
        try:
            if case_set_id:
                # 获取单个测试用例集
                try:
                    case_set = TestCaseSet.objects.get(pk=case_set_id)
                    return json_response(case_set.to_dict())
                except TestCaseSet.DoesNotExist:
                    return json_response(error=f'测试用例集ID {case_set_id} 不存在')
            
            # 获取测试用例集列表
            case_sets = TestCaseSet.objects.all().order_by('-id')
            return json_response([cs.to_dict() for cs in case_sets])
        except Exception as e:
            return json_response(error=f'获取测试用例集失败: {str(e)}')
    
    @auth('exec.task.do')
    def post(self, request):
        """创建新测试用例集"""
        try:
            data = json.loads(request.body)
            name = data.get('name')
            description = data.get('description')
            category = data.get('category')
            test_cases = data.get('test_cases', [])
            
            if not name:
                return json_response(error='测试用例集名称不能为空')
            
            # 创建新测试用例集
            case_set = TestCaseSet.objects.create(
                name=name,
                description=description,
                category=category,
                test_cases=json.dumps(test_cases),
                created_at=human_datetime(),
                created_by_id=request.user.id if hasattr(request, 'user') else 1,
                updated_at=human_datetime(),
                updated_by_id=request.user.id if hasattr(request, 'user') else 1
            )
            
            return json_response(case_set.to_dict())
        except Exception as e:
            return json_response(error=f'创建测试用例集失败: {str(e)}')
    
    @auth('exec.task.do')
    def put(self, request, case_set_id):
        """更新测试用例集"""
        try:
            try:
                case_set = TestCaseSet.objects.get(pk=case_set_id)
            except TestCaseSet.DoesNotExist:
                return json_response(error=f'测试用例集ID {case_set_id} 不存在')
            
            data = json.loads(request.body)
            name = data.get('name')
            description = data.get('description')
            category = data.get('category')
            test_cases = data.get('test_cases')
            
            if not name:
                return json_response(error='测试用例集名称不能为空')
            
            # 更新测试用例集
            if name is not None:
                case_set.name = name
            if description is not None:
                case_set.description = description
            if category is not None:
                case_set.category = category
            if test_cases is not None:
                case_set.test_cases = json.dumps(test_cases)
            
            case_set.updated_at = human_datetime()
            case_set.updated_by_id = request.user.id if hasattr(request, 'user') else 1
            case_set.save()
            
            return json_response(case_set.to_dict())
        except Exception as e:
            return json_response(error=f'更新测试用例集失败: {str(e)}')
    
    @auth('exec.task.do')
    def delete(self, request, case_set_id=None):
        """删除测试用例集"""
        try:
            if not case_set_id:
                data = json.loads(request.body)
                case_set_id = data.get('id')
                
            if not case_set_id:
                return json_response(error='测试用例集ID不能为空')
                
            try:
                case_set = TestCaseSet.objects.get(pk=case_set_id)
            except TestCaseSet.DoesNotExist:
                return json_response(error=f'测试用例集ID {case_set_id} 不存在')
            
            # 删除测试用例集
            case_set.delete()
            
            return json_response()
        except Exception as e:
            return json_response(error=f'删除测试用例集失败: {str(e)}')


class TestCaseSetImportExportView(View):
    """测试用例集导入导出API"""
    
    @auth('exec.task.do')
    def get(self, request):
        """导出测试用例集"""
        try:
            case_set_id = request.GET.get('id')
            if not case_set_id:
                return json_response(error='测试用例集ID不能为空')
                
            try:
                case_set = TestCaseSet.objects.get(pk=case_set_id)
            except TestCaseSet.DoesNotExist:
                return json_response(error=f'测试用例集ID {case_set_id} 不存在')
            
            # 导出测试用例集
            export_data = case_set.to_dict()
            return json_response(export_data)
        except Exception as e:
            return json_response(error=f'导出测试用例集失败: {str(e)}')
    
    @auth('exec.task.do')
    def post(self, request):
        """导入测试用例集"""
        try:
            data = json.loads(request.body)
            import_data = data.get('import_data')
            
            if not import_data:
                return json_response(error='导入数据不能为空')
            
            name = import_data.get('name')
            description = import_data.get('description')
            category = import_data.get('category')
            test_cases = import_data.get('test_cases', [])
            
            if not name:
                return json_response(error='测试用例集名称不能为空')
            
            # 创建新测试用例集
            case_set = TestCaseSet.objects.create(
                name=name,
                description=description,
                category=category,
                test_cases=json.dumps(test_cases),
                created_at=human_datetime(),
                created_by_id=request.user.id if hasattr(request, 'user') else 1,
                updated_at=human_datetime(),
                updated_by_id=request.user.id if hasattr(request, 'user') else 1
            )
            
            return json_response(case_set.to_dict())
        except Exception as e:
            return json_response(error=f'导入测试用例集失败: {str(e)}')


class TestPlanImportExportView(View):
    """测试计划导入导出API"""
    
    @auth('exec.task.do')
    def get(self, request):
        """导出测试计划"""
        try:
            plan_id = request.GET.get('id')
            if not plan_id:
                return json_response(error='测试计划ID不能为空')
                
            try:
                plan = TestPlan.objects.get(pk=plan_id)
            except TestPlan.DoesNotExist:
                return json_response(error=f'测试计划ID {plan_id} 不存在')
            
            # 导出测试计划
            export_data = plan.to_dict()
            return json_response(export_data)
        except Exception as e:
            return json_response(error=f'导出测试计划失败: {str(e)}')
    
    @auth('exec.task.do')
    def post(self, request):
        """导入测试计划"""
        try:
            data = json.loads(request.body)
            import_data = data.get('import_data')
            
            if not import_data:
                return json_response(error='导入数据不能为空')
            
            name = import_data.get('name')
            description = import_data.get('description')
            category = import_data.get('category')
            commands = import_data.get('commands', [])
            files = import_data.get('files', [])
            steps = import_data.get('steps', [])
            step_interval = import_data.get('step_interval', 0)
            file_path_strict = import_data.get('file_path_strict', False)
            variables = import_data.get('variables', [])
            
            if not name:
                return json_response(error='测试计划名称不能为空')
            
            # 创建新测试计划
            plan = TestPlan.objects.create(
                name=name,
                description=description,
                category=category,
                commands=json.dumps(commands),
                files=json.dumps(files),
                steps=json.dumps(steps),
                step_interval=step_interval,
                file_path_strict=file_path_strict,
                variables=json.dumps(variables),
                created_at=human_datetime(),
                created_by_id=request.user.id if hasattr(request, 'user') else 1,
                updated_at=human_datetime(),
                updated_by_id=request.user.id if hasattr(request, 'user') else 1
            )
            
            return json_response(plan.to_dict())
        except Exception as e:
            return json_response(error=f'导入测试计划失败: {str(e)}')


class TestPlanExecutionView(View):
    """测试计划执行API"""
    
    @auth('exec.task.do')
    def get(self, request, execution_id=None):
        """获取测试计划执行记录列表或单个执行记录"""
        try:
            from .models import TestPlanExecution, TestPlanStepResult
            
            if execution_id:
                # 获取单个执行记录
                try:
                    execution = TestPlanExecution.objects.get(id=execution_id) if execution_id.isdigit() else TestPlanExecution.objects.get(token=execution_id)
                    
                    # 获取步骤结果
                    step_results = TestPlanStepResult.objects.filter(execution_id=execution.id).order_by('id')
                    step_results_data = [{
                        'id': sr.id,
                        'step_type': sr.step_type,
                        'step_name': sr.step_name,
                        'command': sr.command,
                        'file_path': sr.file_path,
                        'start_time': sr.start_time,
                        'end_time': sr.end_time,
                        'exit_code': sr.exit_code,
                        'output': sr.output,
                        'status': sr.status,
                        'assertion_enabled': sr.assertion_enabled,
                        'assertion_type': sr.assertion_type,
                        'assertion_config': json.loads(sr.assertion_config) if sr.assertion_config else {},
                        'assertion_status': sr.assertion_status
                    } for sr in step_results]
                    
                    # 构建响应数据
                    execution_data = execution.to_dict()
                    execution_data['step_results'] = step_results_data
                    
                    return json_response(execution_data)
                except (TestPlanExecution.DoesNotExist, ValueError):
                    return json_response(error=f'执行记录 {execution_id} 不存在')
            
            # 获取执行记录列表
            plan_id = request.GET.get('plan_id')
            status = request.GET.get('status')
            limit = int(request.GET.get('limit', 10))
            offset = int(request.GET.get('offset', 0))
            
            query = TestPlanExecution.objects.all()
            
            if plan_id:
                query = query.filter(plan_id=plan_id)
            if status:
                query = query.filter(status=status)
            
            count = query.count()
            records = query.order_by('-id')[offset:offset + limit]
            
            return json_response({
                'count': count,
                'data': [r.to_dict() for r in records]
            })
        except Exception as e:
            return json_response(error=f'获取测试计划执行记录失败: {str(e)}')
    
    @auth('exec.task.do')
    def post(self, request):
        """创建新的测试计划执行记录"""
        try:
            from .models import TestPlanExecution
            
            data = json.loads(request.body)
            plan_id = data.get('plan_id')
            host_ids = data.get('host_ids', [])
            
            if not plan_id:
                return json_response(error='测试计划ID不能为空')
            if not host_ids:
                return json_response(error='目标主机不能为空')
            
            # 获取测试计划
            try:
                plan = TestPlan.objects.get(pk=plan_id)
            except TestPlan.DoesNotExist:
                return json_response(error=f'测试计划ID {plan_id} 不存在')
            
            # 创建执行记录
            token = uuid.uuid4().hex
            execution = TestPlanExecution.objects.create(
                test_plan=plan,
                plan_id=plan.id,
                plan_name=plan.name,
                token=token,
                host_ids=json.dumps(host_ids),
                executor_id=request.user.id if hasattr(request, 'user') else 1,
                executor_name=request.user.username if hasattr(request, 'user') and hasattr(request.user, 'username') else '系统管理员',
                status='pending',
                start_time=human_datetime(),
                total_steps=len(json.loads(plan.steps)) if plan.steps else 0
            )
            
            # 这里可以添加异步任务来执行测试计划
            # 例如: execute_test_plan.delay(execution.id)
            
            return json_response(execution.to_dict())
        except Exception as e:
            return json_response(error=f'创建测试计划执行记录失败: {str(e)}')
    
    @auth('exec.task.do')
    def put(self, request, execution_id):
        """更新测试计划执行记录状态"""
        try:
            from .models import TestPlanExecution
            
            try:
                execution = TestPlanExecution.objects.get(id=execution_id) if execution_id.isdigit() else TestPlanExecution.objects.get(token=execution_id)
            except (TestPlanExecution.DoesNotExist, ValueError):
                return json_response(error=f'执行记录 {execution_id} 不存在')
            
            data = json.loads(request.body)
            status = data.get('status')
            completed_steps = data.get('completed_steps')
            end_time = data.get('end_time')
            
            # 更新执行记录
            if status is not None:
                execution.status = status
            if completed_steps is not None:
                execution.completed_steps = completed_steps
            if end_time is not None:
                execution.end_time = end_time
            elif status == 'completed' or status == 'failed':
                execution.end_time = human_datetime()
            
            execution.save()
            
            return json_response(execution.to_dict())
        except Exception as e:
            return json_response(error=f'更新测试计划执行记录失败: {str(e)}')
    
    @auth('exec.task.do')
    def delete(self, request, execution_id=None):
        """删除测试计划执行记录"""
        try:
            from .models import TestPlanExecution
            
            if not execution_id:
                data = json.loads(request.body)
                execution_id = data.get('id')
                
            if not execution_id:
                return json_response(error='执行记录ID不能为空')
                
            try:
                execution = TestPlanExecution.objects.get(id=execution_id) if execution_id.isdigit() else TestPlanExecution.objects.get(token=execution_id)
            except (TestPlanExecution.DoesNotExist, ValueError):
                return json_response(error=f'执行记录 {execution_id} 不存在')
            
            # 删除执行记录
            execution.delete()
            
            return json_response()
        except Exception as e:
            return json_response(error=f'删除测试计划执行记录失败: {str(e)}')


class TestPlanExecutionHistoryView(View):
    """测试计划执行历史记录API"""
    
    @auth('exec.task.do')
    def get(self, request, plan_id=None):
        """获取测试计划执行历史记录"""
        try:
            from .models import TestPlanExecution
            
            if plan_id:
                # 获取指定测试计划的执行历史
                executions = TestPlanExecution.objects.filter(plan_id=plan_id).order_by('-id')
                return json_response([e.to_dict() for e in executions])
            
            # 获取所有执行历史记录，支持分页和筛选
            limit = int(request.GET.get('limit', 10))
            offset = int(request.GET.get('offset', 0))
            plan_id = request.GET.get('plan_id')
            status = request.GET.get('status')
            
            query = TestPlanExecution.objects.all()
            
            if plan_id:
                query = query.filter(plan_id=plan_id)
            if status:
                query = query.filter(status=status)
            
            count = query.count()
            records = query.order_by('-id')[offset:offset + limit]
            
            return json_response({
                'count': count,
                'data': [r.to_dict() for r in records]
            })
        except Exception as e:
            return json_response(error=f'获取测试计划执行历史记录失败: {str(e)}')


class SampleDataView(View):
    """示例数据管理API - 仅用于开发和测试"""
    
    @auth('exec.task.do')
    def post(self, request):
        """创建示例数据"""
        try:
            data = json.loads(request.body)
            data_type = data.get('type', 'test_plan')
            
            if data_type == 'test_plan':
                # 创建示例测试计划
                plan = TestPlan.objects.create(
                    name='示例测试计划',
                    description='这是一个自动生成的示例测试计划',
                    category='示例',
                    commands=json.dumps([
                        {'name': '示例命令1', 'command': 'echo "Hello World"'},
                        {'name': '示例命令2', 'command': 'ls -la'}
                    ]),
                    files=json.dumps([]),
                    steps=json.dumps([
                        {'type': 'command', 'name': '执行示例命令1', 'command': 'echo "Hello World"'},
                        {'type': 'command', 'name': '执行示例命令2', 'command': 'ls -la'}
                    ]),
                    step_interval=1,
                    file_path_strict=False,
                    variables=json.dumps([]),
                    created_at=human_datetime(),
                    created_by_id=request.user.id if hasattr(request, 'user') else 1,
                    updated_at=human_datetime(),
                    updated_by_id=request.user.id if hasattr(request, 'user') else 1
                )
                return json_response({'message': '示例测试计划已创建', 'id': plan.id})
            elif data_type == 'test_case_set':
                # 创建示例测试用例集
                case_set = TestCaseSet.objects.create(
                    name='示例测试用例集',
                    description='这是一个自动生成的示例测试用例集',
                    category='示例',
                    test_cases=json.dumps([
                        {'name': '示例用例1', 'description': '这是示例用例1', 'expected_result': '预期结果1'},
                        {'name': '示例用例2', 'description': '这是示例用例2', 'expected_result': '预期结果2'}
                    ]),
                    created_at=human_datetime(),
                    created_by_id=request.user.id if hasattr(request, 'user') else 1,
                    updated_at=human_datetime(),
                    updated_by_id=request.user.id if hasattr(request, 'user') else 1
                )
                return json_response({'message': '示例测试用例集已创建', 'id': case_set.id})
            else:
                return json_response(error=f'不支持的数据类型: {data_type}')
        except Exception as e:
            return json_response(error=f'创建示例数据失败: {str(e)}')


class SSHConnectionTestView(View):
    """SSH连接测试API"""
    
    @auth('exec.task.do')
    def post(self, request):
        """测试SSH连接"""
        try:
            data = json.loads(request.body)
            host_id = data.get('host_id')
            
            if not host_id:
                return json_response(error='主机ID不能为空')
            
            # 获取主机信息
            try:
                from apps.host.models import Host
                host = Host.objects.get(pk=host_id)
            except Host.DoesNotExist:
                return json_response(error=f'主机ID {host_id} 不存在')
            
            # 测试SSH连接
            try:
                from libs.ssh import SSH
                ssh = SSH(host.hostname, host.port, host.username, host.private_key)
                ssh.ping()
                return json_response({'message': 'SSH连接成功'})
            except Exception as e:
                return json_response(error=f'SSH连接失败: {str(e)}')
        except Exception as e:
            return json_response(error=f'测试SSH连接失败: {str(e)}')


class TestResultTemplateView(View):
    """测试结果提取模板API"""
    
    @auth('exec.task.do')
    def get(self, request, template_id=None):
        """获取模板列表或单个模板"""
        try:
            from .models import TestResultTemplate
            
            if template_id:
                # 获取单个模板
                try:
                    template = TestResultTemplate.objects.get(pk=template_id)
                    return json_response({
                        'id': template.id,
                        'name': template.name,
                        'description': template.description,
                        'pattern': template.pattern,
                        'example_input': template.example_input,
                        'example_output': template.example_output,
                        'created_at': template.created_at,
                        'updated_at': template.updated_at
                    })
                except TestResultTemplate.DoesNotExist:
                    return json_response(error=f'模板ID {template_id} 不存在')
            
            # 获取模板列表
            templates = TestResultTemplate.objects.all().order_by('-id')
            return json_response([{
                'id': t.id,
                'name': t.name,
                'description': t.description,
                'pattern': t.pattern,
                'created_at': t.created_at,
                'updated_at': t.updated_at
            } for t in templates])
        except Exception as e:
            return json_response(error=f'获取测试结果提取模板失败: {str(e)}')
    
    @auth('exec.task.do')
    def post(self, request):
        """创建新模板"""
        try:
            from .models import TestResultTemplate
            
            data = json.loads(request.body)
            name = data.get('name')
            description = data.get('description')
            pattern = data.get('pattern')
            example_input = data.get('example_input')
            example_output = data.get('example_output')
            
            if not name:
                return json_response(error='模板名称不能为空')
            if not pattern:
                return json_response(error='提取模式不能为空')
            
            # 创建新模板
            template = TestResultTemplate.objects.create(
                name=name,
                description=description,
                pattern=pattern,
                example_input=example_input,
                example_output=example_output,
                created_at=human_datetime(),
                updated_at=human_datetime()
            )
            
            return json_response({
                'id': template.id,
                'name': template.name,
                'description': template.description,
                'pattern': template.pattern,
                'example_input': template.example_input,
                'example_output': template.example_output,
                'created_at': template.created_at,
                'updated_at': template.updated_at
            })
        except Exception as e:
            return json_response(error=f'创建测试结果提取模板失败: {str(e)}')
    
    @auth('exec.task.do')
    def put(self, request, template_id):
        """更新模板"""
        try:
            from .models import TestResultTemplate
            
            try:
                template = TestResultTemplate.objects.get(pk=template_id)
            except TestResultTemplate.DoesNotExist:
                return json_response(error=f'模板ID {template_id} 不存在')
            
            data = json.loads(request.body)
            name = data.get('name')
            description = data.get('description')
            pattern = data.get('pattern')
            example_input = data.get('example_input')
            example_output = data.get('example_output')
            
            if not name:
                return json_response(error='模板名称不能为空')
            if not pattern:
                return json_response(error='提取模式不能为空')
            
            # 更新模板
            template.name = name
            template.description = description
            template.pattern = pattern
            template.example_input = example_input
            template.example_output = example_output
            template.updated_at = human_datetime()
            template.save()
            
            return json_response({
                'id': template.id,
                'name': template.name,
                'description': template.description,
                'pattern': template.pattern,
                'example_input': template.example_input,
                'example_output': template.example_output,
                'created_at': template.created_at,
                'updated_at': template.updated_at
            })
        except Exception as e:
            return json_response(error=f'更新测试结果提取模板失败: {str(e)}')
    
    @auth('exec.task.do')
    def delete(self, request, template_id):
        """删除模板"""
        try:
            from .models import TestResultTemplate
            
            try:
                template = TestResultTemplate.objects.get(pk=template_id)
            except TestResultTemplate.DoesNotExist:
                return json_response(error=f'模板ID {template_id} 不存在')
            
            # 删除模板
            template.delete()
            
            return json_response()
        except Exception as e:
            return json_response(error=f'删除测试结果提取模板失败: {str(e)}')


class RemoteLogFetchView(View):
    """
    远程日志获取视图
    通过SSH连接到远程服务器获取指定路径的日志文件
    """

    @auth('exec.task.do')
    def post(self, request):
        form, error = JsonParser(
            Argument('log_path', help='请输入日志文件路径'),
            Argument('plan_name', help='请输入测试计划名称'),
            Argument('host_id', type=int, help='请选择目标主机'),
            Argument('server_name', required=False, help='远程服务器名称（兼容旧版本）')
        ).parse(request.body)

        if error:
            return json_response(error=error)

        try:
            # 使用主机选择器获取日志内容
            log_content = self._fetch_remote_log_from_host(form.log_path, form.host_id)

            # 获取主机信息用于返回
            host = Host.objects.get(pk=form.host_id)

            return json_response({
                'success': True,
                'content': log_content,
                'plan_name': form.plan_name,
                'log_path': form.log_path,
                'host_info': {
                    'id': host.id,
                    'name': host.name,
                    'hostname': host.hostname
                }
            })

        except Exception as e:
            return json_response(error=f"获取远程日志失败: {str(e)}")

    def _fetch_remote_log_from_host(self, log_path, host_id):
        """
        通过指定主机的SSH连接获取远程日志文件内容
        """
        try:
            # 获取主机对象
            host = Host.objects.get(pk=host_id)

            # 使用主机的SSH连接
            with host.get_ssh() as ssh:
                # 执行cat命令读取日志文件
                command = f"cat {log_path}"
                exit_code, content = ssh.exec_command_raw(command)

                if exit_code != 0:
                    raise Exception(f"读取日志文件失败 (退出码: {exit_code}): {content}")

                if not content.strip():
                    raise Exception("日志文件为空或不存在")

                return content

        except Host.DoesNotExist:
            raise Exception(f"主机不存在 (ID: {host_id})")
        except Exception as e:
            raise Exception(f"SSH连接或文件读取失败: {str(e)}")

    def _fetch_remote_log(self, log_path, server_name='mcp1'):
        """
        通过SSH连接获取远程日志文件内容
        """
        import paramiko
        import io
        
        try:
            # 这里应该从配置或数据库中获取SSH连接信息
            # 暂时使用硬编码的连接信息进行演示
            ssh_config = self._get_ssh_config(server_name)
            
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 建立SSH连接
            ssh.connect(
                hostname=ssh_config['hostname'],
                port=ssh_config.get('port', 22),
                username=ssh_config['username'],
                password=ssh_config.get('password'),
                key_filename=ssh_config.get('key_filename'),
                timeout=30
            )
            
            # 执行cat命令读取日志文件
            command = f"cat {log_path}"
            stdin, stdout, stderr = ssh.exec_command(command)
            
            # 读取输出
            content = stdout.read().decode('utf-8', errors='ignore')
            error_output = stderr.read().decode('utf-8', errors='ignore')
            
            ssh.close()
            
            if error_output and not content:
                raise Exception(f"读取日志文件失败: {error_output}")
            
            return content
            
        except Exception as e:
            raise Exception(f"SSH连接或读取文件失败: {str(e)}")
    
    def _get_ssh_config(self, server_name):
        """
        获取SSH连接配置
        在实际应用中，这应该从数据库或配置文件中获取
        """
        # 尝试从Host模型中获取SSH配置
        try:
            from apps.host.models import Host
            host = Host.objects.filter(name=server_name).first()
            if host:
                return {
                    'hostname': host.hostname,
                    'username': host.username,
                    'password': host.password if hasattr(host, 'password') else None,
                    'key_filename': None,  # 可以根据需要配置SSH密钥
                    'port': host.port
                }
        except:
            pass
        
        # 如果没有找到Host记录，使用MCP服务器的配置
        # 这里根据实际的MCP服务器配置进行调整
        configs = {
            'mcp1': {
                'hostname': '127.0.0.1',  # 替换为实际的MCP服务器IP
                'username': 'admin',      # 替换为实际的用户名
                'password': 'admin',      # 替换为实际的密码
                'key_filename': None,     # 如果使用SSH密钥，指定密钥文件路径
                'port': 22
            }
        }
        
        if server_name not in configs:
            raise Exception(f"未找到服务器 {server_name} 的SSH配置")
        
        return configs[server_name]

